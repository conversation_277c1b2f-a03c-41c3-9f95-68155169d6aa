import { OrderEntity, OrderStatus } from "../marketplace-shared";
import {
  DBOrdersCollection,
  getCurrentDateFirestoreTimestamp,
} from "../services/db.service";
import { processOrderCancellation } from "../services/order-cancellation-service/order-cancellation-service";
import { ExpiredOrdersMonitorLogger } from "./expired-orders-monitor-function.logger";

export async function processExpiredOrders() {
  try {
    ExpiredOrdersMonitorLogger.logProcessingStarted();

    const now = getCurrentDateFirestoreTimestamp();

    // Only cancel orders where sellers failed to send gifts to relayer (status='paid')
    // Orders with status='gift_sent_to_relayer' are not cancelled - buyers can take gifts after deadline
    const expiredOrdersQuery = DBOrdersCollection.where(
      "status",
      "==",
      OrderStatus.PAID
    ).where("deadline", "<", now);

    const expiredOrdersSnapshot = await expiredOrdersQuery.get();

    if (expiredOrdersSnapshot.empty) {
      ExpiredOrdersMonitorLogger.logNoOrdersFound();
      return;
    }

    ExpiredOrdersMonitorLogger.logOrdersFound({
      count: expiredOrdersSnapshot.size,
    });

    for (const orderDoc of expiredOrdersSnapshot.docs) {
      const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

      // Validate order has both buyer and seller
      if (!order.buyerId || !order.sellerId) {
        ExpiredOrdersMonitorLogger.logOrderSkipped({
          orderId: order.id || "unknown",
          reason: "missing_buyer_or_seller",
        });
        continue;
      }

      try {
        const result = await processOrderCancellation(order, order.sellerId);
        ExpiredOrdersMonitorLogger.logOrderProcessed({
          orderId: order.id || "unknown",
          status: "processed",
          message: result.message,
        });
      } catch (error) {
        ExpiredOrdersMonitorLogger.logOrderProcessError({
          error,
          orderId: order.id || "unknown",
        });
      }
    }

    ExpiredOrdersMonitorLogger.logProcessingCompleted();
  } catch (error) {
    ExpiredOrdersMonitorLogger.logProcessingError({ error });
    throw error;
  }
}
