import { logger } from "firebase-functions/v2";
import { LogOperations } from "../constants";

export const TxLookupLogger = {
  logTxLookupGetError(data: { error: unknown }) {
    logger.error(
      "Error getting tx lookup",
      data.error,
      LogOperations.GET_TX_LOOKUP,
      {}
    );
  },

  logTxLookupUpdated(data: { lastCheckedRecordId: string }) {
    logger.info(
      `Updated tx lookup with last_checked_record_id: ${data.lastCheckedRecordId}`,
      LogOperations.UPDATE_TX_LOOKUP,
      {
        lastCheckedRecordId: data.lastCheckedRecordId,
      }
    );
  },

  logTxLookupUpdateError(data: {
    error: unknown;
    lastCheckedRecordId: string;
  }) {
    logger.error(
      "Error updating tx lookup",
      data.error,
      LogOperations.UPDATE_TX_LOOKUP,
      {
        lastCheckedRecordId: data.lastCheckedRecordId,
      }
    );
  },
};
