import { UserType } from "../../marketplace-shared";
import { refundAllActiveProposals } from "../../proposal-functions/proposal-service";
import {
  getUserData,
  requireAuthentication,
  validateOrderCreationParams,
  validatePurchaseParams,
  validateSellerOwnership,
} from "../../services/auth-middleware";
import { validateGiftForLinking } from "../../services/gift-validation.service";
import { createOrder } from "../../services/order-creation-service";
import { validateSellerCreatedOrdersLimit } from "../../services/order-validation-service";
import { processPurchase } from "../../services/purchase-flow-service";
import { validateTelegramIdForOrderOperation } from "../../services/telegram-validation-service";
import { getUserById } from "../../services/user-lookup.service";

export interface CreateOrderAsSellerParams {
  sellerId: string;
  collectionId: string;
  price: number;
  giftId?: string;
}

export interface MakePurchaseAsSellerParams {
  sellerId: string;
  orderId: string;
}

export async function validateCreateOrderRequest(
  request: any,
  params: CreateOrderAsSellerParams
) {
  const authRequest = requireAuthentication(request);
  validateOrderCreationParams(params, UserType.SELLER);
  validateSellerOwnership(authRequest, params.sellerId);

  // Validate user has telegram ID for order operations
  const user = await getUserData(authRequest.auth.uid);
  validateTelegramIdForOrderOperation(user, "create orders");

  return { authRequest, user };
}

export async function validateGiftForOrder(
  giftId: string,
  sellerId: string,
  collectionId: string
): Promise<void> {
  // Parallelize gift validation and user lookup
  const [gift, user] = await Promise.all([
    validateGiftForLinking(giftId),
    getUserById(sellerId),
  ]);

  if (!user || user.tg_id !== gift.owner_tg_id) {
    throw new Error("Gift does not belong to the seller");
  }

  // Validate gift and order are from the same collection
  if (gift.collectionId !== collectionId) {
    throw new Error("Gift collection does not match order collection");
  }
}

export async function validatePurchaseRequest(
  request: any,
  params: MakePurchaseAsSellerParams
) {
  const authRequest = requireAuthentication(request);
  validatePurchaseParams(params, UserType.SELLER);
  validateSellerOwnership(authRequest, params.sellerId);

  // Validate user has telegram ID for purchase operations
  const user = await getUserData(authRequest.auth.uid);
  validateTelegramIdForOrderOperation(user, "make purchases");

  return { authRequest, user };
}

export async function createSellerOrder(params: CreateOrderAsSellerParams) {
  const { sellerId, collectionId, price, giftId } = params;

  // Parallelize gift validation and order limit validation
  const validationPromises = [
    validateSellerCreatedOrdersLimit(sellerId, collectionId),
  ];

  if (giftId) {
    validationPromises.push(
      validateGiftForOrder(giftId, sellerId, collectionId)
    );
  }

  await Promise.all(validationPromises);

  return createOrder({
    userId: sellerId,
    collectionId,
    price,
    giftId: giftId || null,
    userType: UserType.SELLER,
    secondaryMarketPrice: null,
  });
}

export async function processSellerPurchase(
  params: MakePurchaseAsSellerParams
) {
  const { sellerId, orderId } = params;

  // Parallelize proposal refunding and purchase processing preparation
  await refundAllActiveProposals(orderId);

  return processPurchase({
    userId: sellerId,
    orderId,
    userType: UserType.SELLER,
  });
}
