import { logger } from "firebase-functions/v2";
import { LogOperations } from "../../constants";

export const SellerOrderLogger = {
  logCreateOrderError({
    error,
    operation,
    requestData,
    sellerId,
  }: {
    error: unknown;
    operation: LogOperations;
    requestData?: any;
    sellerId?: string;
  }) {
    logger.error("Error creating seller order", {
      operation,
      service: "seller-order-function",
      error,
      sellerId,
      requestData,
    });
  },

  logPurchaseError({
    error,
    operation,
    requestData,
    userId,
  }: {
    error: unknown;
    operation: LogOperations;
    requestData?: any;
    userId?: string;
  }) {
    logger.error("Error in seller purchase", {
      operation,
      service: "seller-order-function",
      error,
      requestData,
      userId,
    });
  },
};
