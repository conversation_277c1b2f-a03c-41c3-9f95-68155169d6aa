import { onCall } from "firebase-functions/v2/https";
import { commonFunctionsConfig, LogOperations } from "../../constants";
import { BuyerOrderLogger } from "./buyer-order-function.logger";
import { BuyerOrderFunctionErrorHandler } from "./buyer-order-function.error-handler";
import {
  createBuyerOrder,
  validatePurchaseRequest,
  processBuyerPurchase,
  validateCreateOrderRequest,
} from "./buyer-order-function.service";

export const createOrderAsBuyer = onCall<{
  buyerId: string;
  collectionId: string;
  price: number;
}>(commonFunctionsConfig, async (request) => {
  const { buyerId, collectionId, price } = request.data;

  try {
    // Validation now returns user data to avoid redundant database calls
    await validateCreateOrderRequest(request, {
      buyerId,
      collectionId,
      price,
    });

    const result = await createBuyerOrder({
      buyerId,
      collectionId,
      price,
    });

    return result;
  } catch (error) {
    BuyerOrderLogger.logCreateOrderError({
      error,
      operation: LogOperations.CREATE_ORDER_AS_BUYER,
      requestData: request.data,
      userId: request.auth?.uid,
    });

    BuyerOrderFunctionErrorHandler.throwCreateOrderError(
      (error as any).message
    );
  }
});

export const makePurchaseAsBuyer = onCall<{
  buyerId: string;
  orderId: string;
}>(commonFunctionsConfig, async (request) => {
  const { buyerId, orderId } = request.data;

  try {
    // Validation now returns user data to avoid redundant database calls
    await validatePurchaseRequest(request, {
      buyerId,
      orderId,
    });

    const result = await processBuyerPurchase({
      buyerId,
      orderId,
    });

    return result;
  } catch (error) {
    BuyerOrderLogger.logPurchaseError({
      error,
      buyerId,
      orderId,
      operation: LogOperations.BUYER_PURCHASE,
    });

    BuyerOrderFunctionErrorHandler.throwPurchaseError((error as any).message);
  }
});
