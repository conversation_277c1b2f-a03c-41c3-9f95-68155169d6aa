import { logger } from "firebase-functions/v2";
import { LogOperations } from "../../constants";

export const BuyerOrderLogger = {
  logCreateOrderError({
    error,
    operation,
    requestData,
    userId,
  }: {
    error: unknown;
    operation: LogOperations;
    requestData?: any;
    userId?: string;
  }) {
    logger.error("Error creating buyer order", {
      operation,
      service: "buyer-order-function",
      error,
      requestData,
      userId,
    });
  },

  logPurchaseError({
    error,
    operation,
    buyerId,
    orderId,
  }: {
    error: unknown;
    operation: LogOperations;
    buyerId?: string;
    orderId?: string;
  }) {
    logger.error("Error in buyer purchase", {
      operation,
      service: "buyer-order-function",
      error,
      buyerId,
      orderId,
    });
  },
};
