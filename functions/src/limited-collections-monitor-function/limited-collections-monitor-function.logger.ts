import { logger } from "firebase-functions/v2";
import { LogOperations } from "../constants";

export const LimitedCollectionsMonitorLogger = {
  logMonitorStarted({ status }: { status: string }) {
    logger.info("Limited collections monitor started", LogOperations.MONITOR, {
      status,
    });
  },

  logCollectionsFound({ count }: { count: number }) {
    logger.info(
      `Found ${count} collections to process`,
      LogOperations.MONITOR,
      { count }
    );
  },

  logCollectionProcessed({
    collectionId,
    status,
  }: {
    collectionId: string;
    status: string;
  }) {
    logger.info(`Processed collection ${collectionId}`, LogOperations.MONITOR, {
      collectionId,
      status,
    });
  },

  logMonitorError({ error }: { error: unknown }) {
    logger.error(
      "Error in limited collections monitor",
      error,
      LogOperations.MONITOR,
      {}
    );
  },

  logMonitorTriggered({
    status,
    timestamp,
  }: {
    status: string;
    timestamp: string;
  }) {
    logger.info(
      "Limited collections monitor triggered",
      LogOperations.MONITOR,
      { status, timestamp }
    );
  },

  logMonitorCompleted() {
    logger.info(
      "Limited collections monitor completed",
      LogOperations.MONITOR,
      {}
    );
  },

  logMonitorFailed({ error, status }: { error: unknown; status: string }) {
    logger.error(
      "Limited collections monitor failed",
      error,
      LogOperations.MONITOR,
      { status }
    );
  },

  logNoGiftsFound() {
    logger.info("No gifts found in result", LogOperations.TELEGRAM_API, {});
  },

  logLimitedGiftsFound({ count }: { count: number }) {
    logger.info(
      "Found limited gifts from Telegram API",
      LogOperations.TELEGRAM_API,
      { count }
    );
  },

  logTelegramApiError({ error }: { error: unknown }) {
    logger.error(
      "Error fetching limited collections from Telegram",
      error,
      LogOperations.TELEGRAM_API,
      {}
    );
  },

  logCollectionNotFound({ collectionId }: { collectionId: string }) {
    logger.error(
      "Collection not found when trying to update to MARKET",
      new Error("Collection not found"),
      LogOperations.COLLECTION_UPDATE,
      { collectionId }
    );
  },

  logLaunchedAtSet({ collectionId }: { collectionId: string }) {
    logger.info(
      "Setting launchedAt for collection",
      LogOperations.COLLECTION_UPDATE,
      { collectionId }
    );
  },

  logLaunchedAtExists({ collectionId }: { collectionId: string }) {
    logger.info(
      "Collection already has launchedAt, skipping",
      LogOperations.COLLECTION_UPDATE,
      { collectionId }
    );
  },

  logCollectionUpdatedToMarket({ collectionId }: { collectionId: string }) {
    logger.info(
      "Updated collection to MARKET status",
      LogOperations.COLLECTION_UPDATE,
      { collectionId }
    );
  },

  logNewCollectionCreated({ collectionId }: { collectionId: string }) {
    logger.info(
      "Created new collection in Firestore",
      LogOperations.COLLECTION_CREATION,
      { collectionId }
    );
  },

  logCollectionCreationNotFound({ collectionId }: { collectionId: string }) {
    logger.info(
      "Collection not found in Firestore, creating new collection",
      LogOperations.COLLECTION_CREATION,
      { collectionId }
    );
  },

  logCollectionCreationFailed({ collectionId }: { collectionId: string }) {
    logger.error(
      "Failed to create collection",
      new Error("Collection creation failed"),
      LogOperations.COLLECTION_CREATION,
      { collectionId }
    );
  },

  logCollectionStatusNotPremarket({
    collectionId,
    status,
  }: {
    collectionId: string;
    status: string;
  }) {
    logger.info(
      "Collection status is not PREMARKET, skipping",
      LogOperations.COLLECTION_PROCESSING,
      { collectionId, status }
    );
  },

  logProcessingCollectionWithUpgradeStars({
    collectionId,
    upgradeStars,
  }: {
    collectionId: string;
    upgradeStars: string;
  }) {
    logger.info(
      "Processing collection with upgradeStars",
      LogOperations.COLLECTION_PROCESSING,
      { collectionId, upgradeStars }
    );
  },

  logCollectionAlreadyExists({ collectionId }: { collectionId: string }) {
    logger.info(
      "Collection already exists in Firestore",
      LogOperations.COLLECTION_EXISTENCE,
      { collectionId }
    );
  },

  logEnsureCollectionError({
    error,
    collectionId,
  }: {
    error: unknown;
    collectionId: string;
  }) {
    logger.error(
      "Error ensuring collection exists",
      error,
      LogOperations.COLLECTION_EXISTENCE,
      { collectionId }
    );
  },

  logNoLimitedCollections() {
    logger.info(
      "No limited collections found from Telegram API",
      LogOperations.MONITOR,
      {}
    );
  },

  logEnsureCollectionFailed({
    error,
    collectionId,
  }: {
    error: unknown;
    collectionId: string;
  }) {
    logger.error(
      "Failed to ensure collection exists",
      error,
      LogOperations.COLLECTION_EXISTENCE,
      { collectionId }
    );
  },

  logNoUpgradeableCollections() {
    logger.info(
      "No upgradeable limited collections found",
      LogOperations.MONITOR,
      {}
    );
  },

  logUpgradeableCollectionsFound({ count }: { count: number }) {
    logger.info(
      "Found upgradeable limited collections",
      LogOperations.MONITOR,
      { count }
    );
  },

  logProcessCollectionFailed({
    error,
    collectionId,
  }: {
    error: unknown;
    collectionId: string;
  }) {
    logger.error(
      "Failed to process collection",
      error,
      LogOperations.COLLECTION_PROCESSING,
      { collectionId }
    );
  },
};
