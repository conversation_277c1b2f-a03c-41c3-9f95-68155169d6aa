import { TRANSACTION_DESCRIPTION_INTL_KEYS } from "../transaction-description-intl-keys";
import { OrderEntity, TxType } from "../marketplace-shared";
import { safeSubtract } from "../utils";
import {
  addFundsWithHistory,
  spendLockedFunds,
} from "./balance-service/balance-service";
import { DBUserCollection } from "./db.service";
import { applyPurchaseFeeWithReferralFromOrder } from "./fee-service/fee-service";

export interface FeeProcessingResult {
  totalFee: number;
  referralFee: number;
  marketplaceFee: number;
  netAmountToSeller: number;
}

/**
 * Processes all purchase fees (marketplace + referral) for an order
 * This function should be called whenever a purchase is made
 */
export async function processFeesOnPurchase(
  order: OrderEntity,
  buyerId: string
): Promise<FeeProcessingResult> {
  // Get buyer's referral information
  const buyerDoc = await DBUserCollection.doc(buyerId).get();
  const buyerData = buyerDoc.data();
  const referralId = buyerData?.referrer_id;

  // Use purchase fee from order object
  const purchaseFeeBPS = order.fees?.purchase_fee ?? 0;
  const referrerFeeBPS = order.fees?.referrer_fee ?? 0;

  // Apply purchase fees with referral logic
  const feeResult = await applyPurchaseFeeWithReferralFromOrder({
    buyerId,
    amount: order.price,
    referralId,
    purchaseFeeBPS,
    referrerFeeBPS,
  });

  // Calculate net amount to seller after fees
  const netAmountToSeller = safeSubtract(order.price, feeResult.totalFee);

  return {
    totalFee: feeResult.totalFee,
    referralFee: feeResult.referralFee,
    marketplaceFee: feeResult.marketplaceFee,
    netAmountToSeller,
  };
}

/**
 * Transfers the net amount to seller after fees have been processed
 * This function should be called when the gift is sent to relayer or purchase is completed
 */
export async function transferNetAmountToSeller(
  order: OrderEntity,
  netAmountToSeller: number,
  buyerId: string
): Promise<void> {
  if (!order.sellerId) {
    throw new Error("Order must have a seller to transfer net amount");
  }

  if (!buyerId) {
    throw new Error("Order must have a buyer to spend locked funds");
  }

  // Spend buyer's locked funds
  await spendLockedFunds(buyerId, order.price);

  // Transfer net amount to seller
  await addFundsWithHistory({
    userId: order.sellerId,
    amount: netAmountToSeller,
    txType: TxType.SELL_FULFILLMENT,
    orderId: order.id,
    descriptionIntlKey:
      TRANSACTION_DESCRIPTION_INTL_KEYS.SALE_COMPLETED_FOR_ORDER,
    descriptionIntlParams: {
      orderNumber: order.number.toString(),
      netAmount: netAmountToSeller.toString(),
    },
  });
}

/**
 * Combined function that processes fees and transfers net amount to seller
 * Used when purchase is completed and gift is already in relayer
 */
export async function processFeesAndTransferToSeller(
  order: OrderEntity,
  buyerId: string
): Promise<FeeProcessingResult> {
  // Process all fees
  const feeResult = await processFeesOnPurchase(order, buyerId);

  // Transfer net amount to seller
  await transferNetAmountToSeller(
    order,
    feeResult.netAmountToSeller,
    order.buyerId || buyerId
  );

  return feeResult;
}
