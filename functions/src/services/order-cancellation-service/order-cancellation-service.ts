import * as admin from "firebase-admin";
import { TRANSACTION_DESCRIPTION_INTL_KEYS } from "../../transaction-description-intl-keys";
import {
  OrderEntity,
  OrderStatus,
  Role,
  TxType,
  UserType,
} from "../../marketplace-shared";
import { refundAllActiveProposals } from "../../proposal-functions/proposal-service";
import { bpsToDecimal, safeMultiply, safeSubtract } from "../../utils";
import {
  addFundsWithHistory,
  spendLockedFunds,
  unlockFundsWithHistory,
} from "../balance-service/balance-service";
import { db, DBOrdersCollection, DBUserCollection } from "../db.service";
import {
  applyFeeToMarketplaceRevenue,
  applyFixedCancelOrderFee,
} from "../fee-service/fee-service";
import { createTransactionRecord } from "../transaction-history-service/transaction-history-service";

async function getAdminUserIds(userIds: string[]) {
  if (userIds.length === 0) return new Set();

  const userDocs = await Promise.all(
    userIds.map((id) => DBUserCollection.doc(id).get())
  );

  const adminIds = new Set<string>();
  userDocs.forEach((doc, index) => {
    if (doc.exists && doc.data()?.role === Role.ADMIN) {
      adminIds.add(userIds[index]);
    }
  });

  return adminIds;
}

async function processSellerCancellation(params: {
  order: OrderEntity;
  sellerLockedAmount: number;
  buyerLockedAmount: number;
}) {
  const { order, sellerLockedAmount, buyerLockedAmount } = params;

  // Parallelize seller penalty and buyer unlock operations
  await Promise.all([
    // Seller penalty
    Promise.resolve().then(async () => {
      await spendLockedFunds(order.sellerId!, sellerLockedAmount);
      await createTransactionRecord({
        userId: order.sellerId!,
        txType: TxType.CANCELATION_FEE,
        amount: sellerLockedAmount,
        orderId: order.id,
        descriptionIntlKey:
          TRANSACTION_DESCRIPTION_INTL_KEYS.CANCELLATION_PENALTY_FOR_SELLER,
        descriptionIntlParams: {
          amount: sellerLockedAmount.toString(),
        },
        isReceivingCompensation: false,
      });
    }),
    // Buyer unlock
    unlockFundsWithHistory({
      userId: order.buyerId!,
      amount: buyerLockedAmount,
      txType: TxType.UNLOCK_COLLATERAL,
      orderId: order.id,
      descriptionIntlKey:
        TRANSACTION_DESCRIPTION_INTL_KEYS.COLLATERAL_UNLOCKED_DUE_TO_SELLER_CANCELLATION,
      descriptionIntlParams: {
        amount: buyerLockedAmount.toString(),
      },
    }),
  ]);
}

async function processBuyerCancellation(params: {
  order: OrderEntity;
  buyerLockedAmount: number;
  sellerLockedAmount: number;
  remainingBuyerCollateral: number;
}) {
  const {
    order,
    buyerLockedAmount,
    sellerLockedAmount,
    remainingBuyerCollateral,
  } = params;

  // Parallelize buyer penalty and seller compensation operations
  await Promise.all([
    // Buyer penalty
    Promise.resolve().then(async () => {
      await spendLockedFunds(order.buyerId!, buyerLockedAmount);
      await createTransactionRecord({
        userId: order.buyerId!,
        txType: TxType.CANCELATION_FEE,
        amount: buyerLockedAmount,
        orderId: order.id,
        descriptionIntlKey:
          TRANSACTION_DESCRIPTION_INTL_KEYS.CANCELLATION_PENALTY_FOR_BUYER,
        descriptionIntlParams: {
          amount: buyerLockedAmount.toString(),
        },
        isReceivingCompensation: false,
      });
    }),
    // Seller compensation
    addFundsWithHistory({
      userId: order.sellerId!,
      amount: remainingBuyerCollateral,
      txType: TxType.CANCELATION_FEE,
      orderId: order.id,
      descriptionIntlKey:
        TRANSACTION_DESCRIPTION_INTL_KEYS.CANCELLATION_COMPENSATION_FROM_BUYER_COLLATERAL,
      descriptionIntlParams: {
        amount: remainingBuyerCollateral.toString(),
      },
      isReceivingCompensation: true,
    }),
    // Seller unlock
    unlockFundsWithHistory({
      userId: order.sellerId!,
      amount: sellerLockedAmount,
      txType: TxType.UNLOCK_COLLATERAL,
      orderId: order.id,
      descriptionIntlKey:
        TRANSACTION_DESCRIPTION_INTL_KEYS.COLLATERAL_UNLOCKED_DUE_TO_BUYER_CANCELLATION,
      descriptionIntlParams: {
        amount: sellerLockedAmount.toString(),
      },
    }),
  ]);
}

async function processAdminOrderCancellation(order: OrderEntity) {
  const hasBothParticipants = Boolean(order.buyerId && order.sellerId);
  const isActiveSinglePersonOrder =
    order.status === OrderStatus.ACTIVE && !hasBothParticipants;
  const isPaidTwoPersonOrder =
    order.status === OrderStatus.PAID && hasBothParticipants;

  if (isActiveSinglePersonOrder) {
    return processAdminSinglePersonCancellation(order);
  } else if (isPaidTwoPersonOrder) {
    return processAdminTwoPersonCancellation(order);
  } else {
    throw new Error(
      `Order ${order.id} is not in a valid state for admin cancellation`
    );
  }
}

async function processAdminSinglePersonCancellation(order: OrderEntity) {
  const batch = db.batch();

  // Update order status to cancelled - no collateral unlocking needed for admin
  batch.update(DBOrdersCollection.doc(order.id!), {
    status: OrderStatus.CANCELLED,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  });

  await batch.commit();

  return {
    success: true,
    message:
      "Order cancelled by admin. All locked collateral has been released without penalties.",
    feeApplied: 0,
    feeType: "none",
  };
}

async function processAdminTwoPersonCancellation(order: OrderEntity) {
  const buyerLockPercentageBPS = order.fees?.buyer_locked_percentage ?? 0;
  const sellerLockPercentageBPS = order.fees?.seller_locked_percentage ?? 0;
  const buyerLockPercentage = buyerLockPercentageBPS / 10000;
  const sellerLockPercentage = sellerLockPercentageBPS / 10000;

  const purchaseFeeBPS = order.fees?.purchase_fee ?? 0;
  const totalFeeAmount =
    purchaseFeeBPS > 0 ? safeMultiply(order.price, purchaseFeeBPS / 10000) : 0;

  const buyerLockedAmount =
    safeMultiply(order.price, buyerLockPercentage) - totalFeeAmount;
  const sellerLockedAmount = safeMultiply(order.price, sellerLockPercentage);

  const userIds = [order.buyerId, order.sellerId].filter(Boolean) as string[];
  const adminUserIds = await getAdminUserIds(userIds);

  // Unlock collateral for both parties without any penalties
  // Skip unlocking funds for admin users
  if (order.buyerId && !adminUserIds.has(order.buyerId)) {
    await unlockFundsWithHistory({
      userId: order.buyerId,
      amount: buyerLockedAmount,
      txType: TxType.UNLOCK_COLLATERAL,
      orderId: order.id,
      descriptionIntlKey:
        TRANSACTION_DESCRIPTION_INTL_KEYS.COLLATERAL_UNLOCKED_DUE_TO_ADMIN_CANCELLATION,
      descriptionIntlParams: {
        amount: buyerLockedAmount.toString(),
      },
    });
  }

  if (order.sellerId && !adminUserIds.has(order.sellerId)) {
    await unlockFundsWithHistory({
      userId: order.sellerId,
      amount: sellerLockedAmount,
      txType: TxType.UNLOCK_COLLATERAL,
      orderId: order.id,
      descriptionIntlKey:
        TRANSACTION_DESCRIPTION_INTL_KEYS.COLLATERAL_UNLOCKED_DUE_TO_ADMIN_CANCELLATION,
      descriptionIntlParams: {
        amount: sellerLockedAmount.toString(),
      },
    });
  }

  // Refund all active proposals before cancelling the order
  await refundAllActiveProposals(order.id!);

  // Update order status to cancelled
  const batch = db.batch();
  batch.update(DBOrdersCollection.doc(order.id!), {
    status: OrderStatus.CANCELLED,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  });

  await batch.commit();

  return {
    success: true,
    message:
      "Order cancelled by admin. All locked collateral has been released without penalties.",
    feeApplied: 0,
    feeType: "none",
  };
}

export async function processOrderCancellation(
  order: OrderEntity,
  cancellingUserId: string
) {
  const cancellingUserDoc = await DBUserCollection.doc(cancellingUserId).get();
  const cancellingUser = cancellingUserDoc.exists
    ? cancellingUserDoc.data()
    : null;
  const isAdminCancelling = cancellingUser?.role === Role.ADMIN;

  if (isAdminCancelling) {
    return processAdminOrderCancellation(order);
  }

  // Route to regular user cancellation functions
  const hasBothParticipants = Boolean(order.buyerId && order.sellerId);
  const isActiveSinglePersonOrder =
    order.status === OrderStatus.ACTIVE && !hasBothParticipants;
  const isPaidTwoPersonOrder =
    order.status === OrderStatus.PAID && hasBothParticipants;
  const isActiveOrderWithGift =
    order.status === OrderStatus.ACTIVE && order.giftId && hasBothParticipants;

  if (isActiveSinglePersonOrder) {
    return processUserSinglePersonCancellation(order, cancellingUserId);
  } else if (isPaidTwoPersonOrder) {
    return processUserTwoPersonCancellation(order, cancellingUserId);
  } else if (isActiveOrderWithGift) {
    return processActiveOrderWithGiftCancellation(order);
  } else {
    throw new Error(
      `Order ${order.id} is not in a valid state for cancellation`
    );
  }
}

async function processUserSinglePersonCancellation(
  order: OrderEntity,
  cancellingUserId: string
) {
  const buyerLockPercentageBPS = order.fees?.buyer_locked_percentage ?? 0;
  const sellerLockPercentageBPS = order.fees?.seller_locked_percentage ?? 0;
  const buyerLockPercentage = bpsToDecimal(buyerLockPercentageBPS);
  const sellerLockPercentage = bpsToDecimal(sellerLockPercentageBPS);

  const buyerLockedAmount = safeMultiply(order.price, buyerLockPercentage);
  const sellerLockedAmount = safeMultiply(order.price, sellerLockPercentage);

  if (order.buyerId === cancellingUserId) {
    await unlockFundsWithHistory({
      userId: order.buyerId,
      amount: buyerLockedAmount,
      txType: TxType.UNLOCK_COLLATERAL,
      orderId: order.id,
      descriptionIntlKey:
        TRANSACTION_DESCRIPTION_INTL_KEYS.COLLATERAL_UNLOCKED_DUE_TO_CANCELLATION,
      descriptionIntlParams: {
        amount: buyerLockedAmount.toString(),
      },
    });
  } else if (order.sellerId === cancellingUserId) {
    await unlockFundsWithHistory({
      userId: order.sellerId,
      amount: sellerLockedAmount,
      txType: TxType.UNLOCK_COLLATERAL,
      orderId: order.id,
      descriptionIntlKey:
        TRANSACTION_DESCRIPTION_INTL_KEYS.COLLATERAL_UNLOCKED_DUE_TO_CANCELLATION,
      descriptionIntlParams: {
        amount: sellerLockedAmount.toString(),
      },
    });
  }

  const feeApplied = await applyFixedCancelOrderFee(cancellingUserId);

  // Refund all active proposals before cancelling the order
  await refundAllActiveProposals(order.id!);

  // Update order status using batch
  const batch = db.batch();
  batch.update(DBOrdersCollection.doc(order.id!), {
    status: OrderStatus.CANCELLED,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  });

  await batch.commit();

  const feeMessage =
    feeApplied > 0
      ? ` A cancellation fee of ${feeApplied} TON was applied.`
      : "";

  return {
    success: true,
    message: `Order cancelled successfully. Locked funds have been released.${feeMessage}`,
    feeApplied,
    feeType: "fixed",
  };
}

async function processUserTwoPersonCancellation(
  order: OrderEntity,
  cancellingUserId: string
) {
  const cancelFeePercentageBPS = order.fees?.order_cancellation_fee ?? 0;
  const buyerLockPercentageBPS = order.fees?.buyer_locked_percentage ?? 0;
  const sellerLockPercentageBPS = order.fees?.seller_locked_percentage ?? 0;
  const cancelFeePercentage = bpsToDecimal(cancelFeePercentageBPS);
  const buyerLockPercentage = bpsToDecimal(buyerLockPercentageBPS);
  const sellerLockPercentage = bpsToDecimal(sellerLockPercentageBPS);

  const purchaseFeeBPS = order.fees?.purchase_fee ?? 0;
  const totalFeeAmount =
    purchaseFeeBPS > 0
      ? safeMultiply(order.price, bpsToDecimal(purchaseFeeBPS))
      : 0;

  const buyerLockedAmount =
    safeMultiply(order.price, buyerLockPercentage) - totalFeeAmount;
  const sellerLockedAmount = safeMultiply(order.price, sellerLockPercentage);

  // Calculate marketplace fee from buyer's collateral (not full order price)
  const marketplaceFee = safeMultiply(buyerLockedAmount, cancelFeePercentage);
  const remainingBuyerCollateral = safeSubtract(
    buyerLockedAmount,
    marketplaceFee
  );

  // Determine who is cancelling and apply appropriate logic
  if (cancellingUserId === order.sellerId) {
    await processSellerCancellation({
      order,
      sellerLockedAmount,
      buyerLockedAmount,
    });
  } else if (cancellingUserId === order.buyerId) {
    await processBuyerCancellation({
      order,
      buyerLockedAmount,
      sellerLockedAmount,
      remainingBuyerCollateral,
    });
  }

  // Parallelize fee operations and proposal refunding
  const feeOperations = [];

  // Apply marketplace fee
  if (marketplaceFee > 0) {
    feeOperations.push(
      applyFeeToMarketplaceRevenue({
        feeAmount: marketplaceFee,
        feeType: "cancel_order_penalty",
      })
    );
  }

  // Handle reseller earnings for seller
  const resellerEarnings = order.reseller_earnings_for_seller ?? 0;
  if (resellerEarnings > 0) {
    if (cancellingUserId === order.sellerId) {
      // Seller cancels: reseller earnings go to marketplace revenue
      feeOperations.push(
        applyFeeToMarketplaceRevenue({
          feeAmount: resellerEarnings,
          feeType: "reseller_earnings_seller_cancel",
        })
      );
    } else if (cancellingUserId === order.buyerId) {
      // Buyer cancels: reseller earnings go to seller
      feeOperations.push(
        addFundsWithHistory({
          userId: order.sellerId!,
          amount: resellerEarnings,
          txType: TxType.RESELL_FEE_EARNINGS,
          orderId: order.id,
          description: `Resell fee earnings from buyer cancellation (${resellerEarnings} TON)`,
        })
      );
    }
  }

  // Parallelize fee operations, proposal refunding, and order status update
  await Promise.all([
    ...feeOperations,
    refundAllActiveProposals(order.id!),
    // Update order status to cancelled
    DBOrdersCollection.doc(order.id!).update({
      status: OrderStatus.CANCELLED,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    }),
  ]);

  const cancellerRole =
    cancellingUserId === order.sellerId ? UserType.SELLER : UserType.BUYER;
  const compensatedRole =
    cancellingUserId === order.sellerId ? UserType.BUYER : UserType.SELLER;

  const compensationAmount =
    cancellingUserId === order.sellerId
      ? buyerLockedAmount // Seller cancelled, buyer gets full collateral back
      : remainingBuyerCollateral; // Buyer cancelled, seller gets remaining collateral after fees

  return {
    success: true,
    message: `Order cancelled by ${cancellerRole}. ${compensatedRole} received ${compensationAmount} TON compensation. Marketplace fee: ${marketplaceFee} TON.`,
    feeApplied: marketplaceFee,
    feeType: "dynamic",
  };
}

export async function validateCancellationPermission(
  order: OrderEntity,
  userId: string
) {
  // Check if the user is an admin - admins can cancel any order
  const userDoc = await DBUserCollection.doc(userId).get();
  const userData = userDoc.exists ? userDoc.data() : null;
  const isAdmin = userData?.role === Role.ADMIN;

  // Skip permission validation for admin users
  if (!isAdmin) {
    if (order.buyerId !== userId && order.sellerId !== userId) {
      throw new Error(
        "You can only cancel orders where you are the buyer or seller."
      );
    }
  }

  if (order.status === OrderStatus.FULFILLED) {
    throw new Error("Cannot cancel a fulfilled order.");
  }

  if (order.status === OrderStatus.CANCELLED) {
    throw new Error("Order is already cancelled.");
  }

  if (order.status === OrderStatus.GIFT_SENT_TO_RELAYER) {
    throw new Error(
      "Cannot cancel an order where the gift has already been sent to relayer."
    );
  }

  // Special validation for CREATED orders
  if (order.status === OrderStatus.CREATED) {
    // Only sellers can cancel CREATED orders, and only if they are the seller
    if (order.sellerId !== userId) {
      throw new Error("Only the seller can cancel a CREATED order.");
    }
    throw new Error(
      "Cannot cancel a CREATED order. Please wait for a buyer or let it expire."
    );
  }
}

/**
 * Handles cancellation of ACTIVE orders that have a gift field
 * No cancellation fees applied to seller, no seller collateral unlocked
 */
async function processActiveOrderWithGiftCancellation(order: OrderEntity) {
  if (!order.id) {
    throw new Error("Order ID is required for cancellation");
  }

  // Update order status to cancelled
  await DBOrdersCollection.doc(order.id).update({
    status: OrderStatus.CANCELLED,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  });

  // Unlock buyer collateral (if any) without fees
  if (order.buyerId) {
    const buyerLockPercentageBPS = order.fees?.buyer_locked_percentage ?? 0;
    const buyerLockPercentage = bpsToDecimal(buyerLockPercentageBPS);
    const buyerLockedAmount = safeMultiply(order.price, buyerLockPercentage);

    if (buyerLockedAmount > 0) {
      await unlockFundsWithHistory({
        userId: order.buyerId,
        amount: buyerLockedAmount,
        txType: TxType.UNLOCK_COLLATERAL,
        orderId: order.id,
        descriptionIntlKey:
          TRANSACTION_DESCRIPTION_INTL_KEYS.UNLOCKED_BUYER_COLLATERAL_FOR_CANCELLED_ORDER,
        descriptionIntlParams: {
          orderNumber: order.number.toString(),
          amount: buyerLockedAmount.toString(),
        },
      });
    }
  }

  // No seller collateral to unlock since gift is in relayer
  // No cancellation fees applied

  return {
    success: true,
    message:
      "Order cancelled successfully. No fees applied since gift is in relayer.",
    orderNumber: order.number,
    refundedAmount: 0, // No seller refund needed
    feeAmount: 0, // No cancellation fees
    feeType: "none" as const,
  };
}
