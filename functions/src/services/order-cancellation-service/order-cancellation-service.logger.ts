import { logger } from "firebase-functions/v2";
import { LogOperations } from "../../constants";

export const OrderCancellationServiceLogger = {
  logOrderCancellationStarted({
    orderId,
    userId,
    operation,
  }: {
    orderId: string;
    userId: string;
    operation: string;
  }) {
    logger.info(
      "Order cancellation started",
      LogOperations.CANCELLATION_STARTED,
      {
        orderId,
        userId,
        operation,
      }
    );
  },

  logOrderCancellationCompleted({
    orderId,
    userId,
    feeApplied,
    feeType,
  }: {
    orderId: string;
    userId: string;
    feeApplied: boolean;
    feeType?: string;
  }) {
    logger.info(
      "Order cancellation completed",
      LogOperations.CANCELLATION_COMPLETED,
      {
        orderId,
        userId,
        feeApplied,
        feeType,
      }
    );
  },

  logOrderCancellationError({
    error,
    operation,
    orderId,
    userId,
  }: {
    error: unknown;
    operation: string;
    orderId?: string;
    userId?: string;
  }) {
    logger.error(
      `Error in order cancellation: ${operation}`,
      error,
      LogOperations.CANCEL_ORDER,
      {
        orderId,
        userId,
      }
    );
  },
};
