import { HttpsError } from "firebase-functions/v2/https";
import { AUTH_ERRORS } from "../error-messages";
import { UserEntity } from "../marketplace-shared";

export function requireTelegramId(user: UserEntity, operation?: string): void {
  if (!user.tg_id) {
    const fallbackMessage = operation
      ? `User must have a Telegram ID configured to ${operation}.`
      : "User must have a Telegram ID configured to perform this operation.";

    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: AUTH_ERRORS.TELEGRAM_ID_REQUIRED,
        fallbackMessage,
      })
    );
  }
}

// Alias for backward compatibility
export const validateTelegramIdForOrderOperation = requireTelegramId;
