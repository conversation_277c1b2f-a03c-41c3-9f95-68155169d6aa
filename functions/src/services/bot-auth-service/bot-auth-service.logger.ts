import { logger } from "firebase-functions/v2";
import { LogOperations } from "../../constants";

export function logBotAuthSuccess({ operation }: { operation: string }) {
  logger.info("Bot authentication successful", {
    operation: LogOperations.BOT_OPERATION,
    service: "bot-auth-service",
    operationName: operation,
  });
}

export function logBotAuthError({
  error,
  operation,
}: {
  error: unknown;
  operation: string;
}) {
  logger.error(`Error in bot authentication: ${operation}`, {
    operation: LogOperations.BOT_OPERATION,
    service: "bot-auth-service",
    error,
    operationName: operation,
  });
}
