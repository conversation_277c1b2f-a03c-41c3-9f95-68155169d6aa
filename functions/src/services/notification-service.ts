/* eslint-disable no-unused-vars */
import fetch from "node-fetch";
import { getEnv } from "../config";
import { logger } from "firebase-functions/v2";
import { LogOperations } from "../constants";

export interface NotifyBuyParams {
  orderId: string;
  sellerId: string;
  orderNumber?: number;
  price?: number;
}

export interface NotifyGiftSendParams {
  orderId: string;
  buyerId: string;
  orderNumber?: number;
}

export interface NotificationResult {
  success: boolean;
  message: string;
}

export async function notifySellerOrderPaid(
  params: NotifyBuyParams
): Promise<NotificationResult> {
  try {
    const { orderId, sellerId, orderNumber, price } = params;

    logger.info(
      "Sending seller order paid notification",
      LogOperations.BOT_OPERATION,
      {
        orderId,
        sellerId,
        orderNumber,
        price,
      }
    );

    const botAppUrl = getEnv().url.webhook_url;
    if (!botAppUrl) {
      throw new Error("Bot webhook URL not configured");
    }

    const authToken = process.env.AUTH_TOKEN;
    if (!authToken) {
      throw new Error("AUTH_TOKEN not configured for bot notifications");
    }

    const notificationUrl = `${botAppUrl}/notify-buy`;

    const response = await fetch(notificationUrl, {
      method: "POST",
      timeout: 10000, // 10 second timeout
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
        "User-Agent": "marketplace-functions/notification",
      },
      body: JSON.stringify({
        orderId,
        sellerId,
        orderNumber,
        price,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `Bot notification failed: ${response.status} ${response.statusText} - ${errorText}`
      );
    }

    const responseData = await response.json();

    logger.info(
      "Seller notification sent successfully",
      LogOperations.BOT_OPERATION,
      {
        orderId,
        sellerId,
        responseData,
      }
    );

    return {
      success: true,
      message: "Seller notification sent successfully",
    };
  } catch (error) {
    logger.error(
      "Failed to send seller notification",
      error,
      LogOperations.BOT_OPERATION,
      {
        orderId: params.orderId,
        sellerId: params.sellerId,
      }
    );

    return {
      success: false,
      message: `Failed to send notification: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    };
  }
}

export async function notifyBuyerGiftSent(
  params: NotifyGiftSendParams
): Promise<NotificationResult> {
  try {
    const { orderId, buyerId, orderNumber } = params;

    logger.info(
      "Sending buyer gift sent notification",
      LogOperations.BOT_OPERATION,
      {
        orderId,
        buyerId,
        orderNumber,
      }
    );

    const botAppUrl = getEnv().url.webhook_url;
    if (!botAppUrl) {
      throw new Error("Bot webhook URL not configured");
    }

    const authToken = process.env.AUTH_TOKEN;
    if (!authToken) {
      throw new Error("AUTH_TOKEN not configured for bot notifications");
    }

    const notificationUrl = `${botAppUrl}/notify-gift-send`;

    const response = await fetch(notificationUrl, {
      method: "POST",
      timeout: 10000, // 10 second timeout
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
        "User-Agent": "marketplace-functions/notification",
      },
      body: JSON.stringify({
        orderId,
        buyerId,
        orderNumber,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `Bot notification failed: ${response.status} ${response.statusText} - ${errorText}`
      );
    }

    const responseData = await response.json();

    logger.info(
      "Buyer notification sent successfully",
      LogOperations.BOT_OPERATION,
      {
        orderId,
        buyerId,
        responseData,
      }
    );

    return {
      success: true,
      message: "Buyer notification sent successfully",
    };
  } catch (error) {
    logger.error(
      "Failed to send buyer notification",
      error,
      LogOperations.BOT_OPERATION,
      {
        orderId: params.orderId,
        buyerId: params.buyerId,
      }
    );

    return {
      success: false,
      message: `Failed to send notification: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    };
  }
}

export async function sendNotificationSafely<
  T extends NotifyBuyParams | NotifyGiftSendParams
>(
  notificationFn: (params: T) => Promise<NotificationResult>,
  params: T,
  context: string
): Promise<void> {
  try {
    const result = await notificationFn(params);
    if (!result.success) {
      logger.error(
        `Notification failed in ${context}`,
        new Error(result.message),
        LogOperations.BOT_OPERATION,
        params
      );
    }
  } catch (error) {
    logger.error(
      `Notification error in ${context}`,
      error,
      LogOperations.BOT_OPERATION,
      params
    );
  }
}
