import * as admin from "firebase-admin";
import {
  AppDate,
  TxType,
  UserTxEntity,
} from "../../marketplace-shared";
import { applyTransactionSign } from "../../utils/transaction-sign-utils";
import { DBUserCollection } from "../db.service";
import {
  TransactionHistoryServiceLogger,
  logTransactionHistoryError,
} from "./transaction-history-service.logger";

const USER_TX_HISTORY_SUBCOLLECTION = "tx_history";

export interface CreateTransactionParams {
  userId: string;
  txType: TxType;
  amount: number;
  orderId?: string;
  description?: string; // fallback for existing records
  descriptionIntlKey?: string; // internationalization key
  descriptionIntlParams?: Record<string, string | number>; // parameters for intl message
  isReceivingCompensation?: boolean; // For CANCELATION_FEE only
}

export async function createTransactionRecord(
  params: CreateTransactionParams
): Promise<void> {
  const {
    userId,
    txType,
    amount,
    orderId,
    description,
    descriptionIntlKey,
    descriptionIntlParams,
    isReceivingCompensation,
  } = params;

  try {
    // Apply standardized sign to the amount
    const signedAmount = applyTransactionSign(
      amount,
      txType,
      isReceivingCompensation
    );

    const txRecord: UserTxEntity = {
      tx_type: txType,
      user_id: userId,
      amount: signedAmount,
      order_id: orderId,
      description,
      description_intl_key: descriptionIntlKey,
      description_intl_params: descriptionIntlParams,
      createdAt: admin.firestore.FieldValue.serverTimestamp() as AppDate,
    };

    // Store transaction as sub-collection under the user
    await DBUserCollection.doc(userId)
      .collection(USER_TX_HISTORY_SUBCOLLECTION)
      .add(txRecord);

    TransactionHistoryServiceLogger.logTransactionCreated({
      userId,
      txType,
      amount: signedAmount,
    });
  } catch (error) {
    logTransactionHistoryError({
      error,
      operation: "create_transaction_record",
      userId,
      txType,
    });
    // Don't throw error to avoid breaking main operations
    // Transaction history is supplementary data
  }
}
