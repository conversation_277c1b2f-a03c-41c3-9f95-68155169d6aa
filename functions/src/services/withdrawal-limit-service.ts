import { WithdrawalLimitLogger } from "../logger/withdrawal-limit-logger";
import {
  AppDate,
  firebaseTimestampToDate,
  formatDateToFirebaseTimestamp,
  UserEntity,
  Withdrawal24h,
} from "../marketplace-shared";
import {
  db,
  DBUserCollection,
  getCurrentDateFirestoreTimestamp,
} from "./db.service";

const HOURS_24_IN_MS = 24 * 60 * 60 * 1000;

export interface WithdrawalLimitInfo {
  currentWithdrawn: number;
  remainingLimit: number;
  canWithdraw: boolean;
  resetAt: Date;
}

export interface CheckWithdrawalLimitParams {
  userId: string;
  requestedAmount: number;
  maxWithdrawalAmount: number;
}

export interface UpdateWithdrawalTrackingParams {
  userId: string;
  withdrawnAmount: number;
}

export async function checkWithdrawalLimit(
  params: CheckWithdrawalLimitParams
): Promise<WithdrawalLimitInfo> {
  const { userId, requestedAmount, maxWithdrawalAmount } = params;

  try {
    const userDoc = await DBUserCollection.doc(userId).get();

    if (!userDoc.exists) {
      throw new Error(`User ${userId} not found`);
    }

    const userData = userDoc.data() as UserEntity;
    const now = getCurrentDateFirestoreTimestamp();
    const currentTime = now.toDate();

    let currentWithdrawn = 0;
    let resetAt = new Date(currentTime.getTime() + HOURS_24_IN_MS);

    // Check if user has withdrawal tracking data
    if (userData.withdrawal_24h) {
      const lastResetTime = firebaseTimestampToDate(
        userData.withdrawal_24h.lastResetAt
      );
      const timeSinceReset = currentTime.getTime() - lastResetTime.getTime();

      // If less than 24 hours have passed since last reset, use existing data
      if (timeSinceReset < HOURS_24_IN_MS) {
        currentWithdrawn = userData.withdrawal_24h.amount;
        resetAt = new Date(lastResetTime.getTime() + HOURS_24_IN_MS);
      }
      // If 24+ hours have passed, the limit has reset (currentWithdrawn stays 0)
    }

    const remainingLimit = Math.max(0, maxWithdrawalAmount - currentWithdrawn);
    const canWithdraw = requestedAmount <= remainingLimit;

    const limitInfo: WithdrawalLimitInfo = {
      currentWithdrawn,
      remainingLimit,
      canWithdraw,
      resetAt,
    };

    WithdrawalLimitLogger.logLimitCheckCompleted({
      userId,
      requestedAmount,
      maxWithdrawalAmount,
      currentWithdrawn,
      remainingLimit,
      canWithdraw,
    });

    return limitInfo;
  } catch (error) {
    WithdrawalLimitLogger.logLimitCheckError({
      error,
      userId,
      requestedAmount,
      maxWithdrawalAmount,
    });
    throw error;
  }
}

export async function updateWithdrawalTracking(
  params: UpdateWithdrawalTrackingParams
): Promise<void> {
  const { userId, withdrawnAmount } = params;

  try {
    const userRef = DBUserCollection.doc(userId);

    await db.runTransaction(async (transaction) => {
      const userDoc = await transaction.get(userRef);

      if (!userDoc.exists) {
        throw new Error(`User ${userId} not found`);
      }

      const userData = userDoc.data() as UserEntity;
      const now = getCurrentDateFirestoreTimestamp();
      const currentTime = now.toDate();

      let newWithdrawnAmount = withdrawnAmount;
      let resetTime = now;

      // Check if user has existing withdrawal tracking
      if (userData.withdrawal_24h) {
        const lastResetTime = firebaseTimestampToDate(
          userData.withdrawal_24h.lastResetAt
        );
        const timeSinceReset = currentTime.getTime() - lastResetTime.getTime();

        // If less than 24 hours have passed, add to existing amount
        if (timeSinceReset < HOURS_24_IN_MS) {
          newWithdrawnAmount = userData.withdrawal_24h.amount + withdrawnAmount;
          resetTime = formatDateToFirebaseTimestamp(
            userData.withdrawal_24h.lastResetAt
          );
        }
        // If 24+ hours have passed, start fresh with new amount and reset time
      }

      const newWithdrawalTracking: Withdrawal24h = {
        amount: newWithdrawnAmount,
        lastResetAt: resetTime as AppDate,
      };

      transaction.update(userRef, {
        withdrawal_24h: newWithdrawalTracking,
      });

      WithdrawalLimitLogger.logTrackingUpdated({
        userId,
        withdrawnAmount,
        newWithdrawnAmount,
        resetTime: resetTime.toDate(),
      });
    });
  } catch (error) {
    WithdrawalLimitLogger.logTrackingUpdateError({
      error,
      userId,
      withdrawnAmount,
    });
    throw error;
  }
}

export async function getWithdrawalStatus(
  userId: string,
  maxWithdrawalAmount: number
): Promise<WithdrawalLimitInfo> {
  return checkWithdrawalLimit({
    userId,
    requestedAmount: 0,
    maxWithdrawalAmount,
  });
}
