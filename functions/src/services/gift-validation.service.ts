import { GiftStatus, OrderStatus } from "../marketplace-shared";
import { DBOrdersCollection } from "./db.service";
import { getGiftById } from "./gift-service";

export async function isGiftUsedInOrder(giftId: string): Promise<boolean> {
  try {
    const ordersQuery = await DBOrdersCollection.where(
      "giftId",
      "==",
      giftId
    ).get();

    // Check if any order with this giftId has status: paid, active, or gift_sent_to_relayer
    for (const orderDoc of ordersQuery.docs) {
      const orderData = orderDoc.data();
      const status = orderData.status;

      if (
        status === OrderStatus.PAID ||
        status === OrderStatus.ACTIVE ||
        status === OrderStatus.GIFT_SENT_TO_RELAYER
      ) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error("Error checking if gift is used in order:", error);
    throw new Error("Failed to validate gift usage");
  }
}

export async function validateGiftForLinking(giftId: string) {
  // 1. Check if gift exists
  const gift = await getGiftById(giftId);
  if (!gift) {
    throw new Error("Gift not found");
  }

  // 2. Check if gift has status DEPOSITED
  if (gift.status !== GiftStatus.DEPOSITED) {
    throw new Error("Gift must have status 'deposited' to be linked");
  }

  // 3. Check if gift is already used in another order
  const isUsed = await isGiftUsedInOrder(giftId);
  if (isUsed) {
    throw new Error("Gift is already linked to another order");
  }

  // 4. Validate gift ownership (this will be done in the calling function
  // since it requires user lookup)

  return gift;
}
