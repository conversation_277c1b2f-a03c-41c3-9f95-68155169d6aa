import { LogOperations } from "../../constants";
import { TRANSACTION_DESCRIPTION_INTL_KEYS } from "../../transaction-description-intl-keys";
import {
  MARKETPLACE_REVENUE_USER_ID,
  Role,
  TxType,
  UserEntity,
} from "../../marketplace-shared";
import { bpsToDecimal, safeMultiply, safeSubtract } from "../../utils";
import {
  addFunds,
  addFundsWithHistory,
  updateUserBalance,
  validateSufficientFunds,
} from "../balance-service/balance-service";
import { DBUserCollection } from "../db.service";
import { getCachedAppConfig } from "../performance-cache-service";
import { createTransactionRecord } from "../transaction-history-service/transaction-history-service";
import { FeeServiceLogger } from "./fee-service.logger";

export async function getAppConfig() {
  try {
    // Use cached version for better performance
    return getCachedAppConfig();
  } catch (error) {
    FeeServiceLogger.logAppConfigNotFound();
    FeeServiceLogger.logFeeServiceError({
      error,
      operation: LogOperations.APP_CONFIG_FETCH,
    });

    // Return default config as fallback
    return {
      deposit_fee: 0,
      withdrawal_fee: 0,
      withdrawal_gift_fee: 0,
      referrer_fee: 0,
      cancel_order_fee: 0,
      purchase_fee: 0,
      buyer_lock_percentage: 0,
      seller_lock_percentage: 0,
      resell_purchase_fee: 0,
      resell_purchase_fee_for_seller: 0,
      min_deposit_amount: 0,
      min_withdrawal_amount: 0,
      max_withdrawal_amount: 0,
      min_secondary_market_price: 0,
      fixed_cancel_order_fee: 0,
      cancel_price_proposal_fee: 0,
      lock_period: 21, // Default 21 days lock period
    };
  }
}

export function calculateFeeAmount(amount: number, feeBps: number) {
  if (!feeBps || feeBps <= 0) {
    return 0;
  }
  // BPS = basis points (1 BPS = 0.01%)
  return safeMultiply(amount, bpsToDecimal(feeBps));
}

export async function getAdminUser() {
  try {
    const adminQuery = await DBUserCollection.where("role", "==", Role.ADMIN)
      .limit(1)
      .get();

    if (adminQuery.empty) {
      FeeServiceLogger.logAdminUserNotFound();
      return null;
    }

    const adminDoc = adminQuery.docs[0];
    return {
      // @ts-expect-error note
      id: adminDoc.id,
      ...adminDoc.data(),
    } as UserEntity;
  } catch (error) {
    FeeServiceLogger.logFeeServiceError({
      error,
      operation: LogOperations.ADMIN_USER_LOOKUP,
    });
    throw error;
  }
}

export async function applyFeeToMarketplaceRevenue(params: {
  feeAmount: number;
  feeType: string;
}) {
  const { feeAmount, feeType } = params;
  if (feeAmount <= 0) {
    return;
  }

  try {
    await addFunds(MARKETPLACE_REVENUE_USER_ID, feeAmount);
    FeeServiceLogger.logFeeApplied({
      feeAmount,
      feeType,
      userId: MARKETPLACE_REVENUE_USER_ID,
    });
  } catch (error) {
    FeeServiceLogger.logFeeServiceError({
      error,
      operation: LogOperations.MARKETPLACE_REVENUE,
      amount: feeAmount,
    });
    throw error;
  }
}

export async function applyDepositFee(params: { depositAmount: number }) {
  const { depositAmount } = params;
  try {
    const config = await getAppConfig();
    if (!config?.deposit_fee) {
      return depositAmount;
    }

    // Deposit fee is now a static TON value, not BPS
    const feeAmount = config.deposit_fee;
    if (feeAmount <= 0 || feeAmount >= depositAmount) {
      return depositAmount;
    }

    const netAmount = safeSubtract(depositAmount, feeAmount);

    await applyFeeToMarketplaceRevenue({
      feeAmount,
      feeType: "deposit",
    });

    FeeServiceLogger.logFeeApplied({
      feeAmount,
      feeType: "deposit",
      netAmount,
    });

    return netAmount;
  } catch (error) {
    FeeServiceLogger.logFeeServiceError({
      error,
      operation: LogOperations.DEPOSIT_FEE,
      amount: depositAmount,
    });
    throw error;
  }
}

export async function applyFixedCancelOrderFee(userId: string) {
  try {
    const config = await getAppConfig();
    if (!config?.fixed_cancel_order_fee) {
      return 0;
    }

    // Fixed cancel order fee is a static TON value, not BPS
    const feeAmount = config.fixed_cancel_order_fee;
    if (feeAmount <= 0) {
      return 0;
    }

    await validateSufficientFunds({
      userId,
      amount: feeAmount,
      operation: "fixed cancel order fee",
    });

    await updateUserBalance({ userId, sumChange: -feeAmount, lockedChange: 0 });

    // Record transaction history for penalty payment
    await createTransactionRecord({
      userId,
      txType: TxType.CANCELATION_FEE,
      amount: feeAmount,
      descriptionIntlKey:
        TRANSACTION_DESCRIPTION_INTL_KEYS.FIXED_CANCELLATION_FEE_PENALTY,
      descriptionIntlParams: {
        amount: feeAmount.toString(),
      },
      isReceivingCompensation: false,
    });

    await applyFeeToMarketplaceRevenue({
      feeAmount,
      feeType: "fixed_cancel_order",
    });

    FeeServiceLogger.logFeeApplied({
      feeAmount,
      feeType: "fixed_cancel_order",
      userId,
    });

    return feeAmount;
  } catch (error) {
    FeeServiceLogger.logFeeServiceError({
      error,
      operation: LogOperations.FIXED_CANCEL_ORDER_FEE,
      userId,
    });
    throw error;
  }
}

export async function applyResellPurchaseFee(params: {
  buyerId: string;
  amount: number;
  resellPurchaseFeeBPS: number;
}) {
  const { buyerId, amount, resellPurchaseFeeBPS } = params;
  try {
    if (!resellPurchaseFeeBPS || resellPurchaseFeeBPS <= 0) {
      return { totalFee: 0, referralFee: 0, marketplaceFee: 0 };
    }

    const totalFeeAmount = calculateFeeAmount(amount, resellPurchaseFeeBPS);
    if (totalFeeAmount <= 0) {
      return { totalFee: 0, referralFee: 0, marketplaceFee: 0 };
    }

    // Validate buyer has sufficient funds for total fee
    await validateSufficientFunds({
      userId: buyerId,
      amount: totalFeeAmount,
      operation: "resell purchase fee",
    });

    // Deduct total fee from buyer
    await updateUserBalance({
      userId: buyerId,
      sumChange: -totalFeeAmount,
      lockedChange: 0,
    });

    // No referral fees for reselling - all fee goes to marketplace
    const marketplaceFeeAmount = totalFeeAmount;

    // Apply fee to marketplace revenue
    await applyFeeToMarketplaceRevenue({
      feeAmount: marketplaceFeeAmount,
      feeType: "resell_purchase",
    });

    FeeServiceLogger.logTotalFeeApplied({
      feeAmount: totalFeeAmount,
      feeType: "resell_purchase_total",
      referralFee: 0,
      marketplaceFee: marketplaceFeeAmount,
    });

    return {
      totalFee: totalFeeAmount,
      referralFee: 0,
      marketplaceFee: marketplaceFeeAmount,
    };
  } catch (error) {
    FeeServiceLogger.logFeeServiceError({
      error,
      operation: LogOperations.RESELL_PURCHASE_FEE,
    });
    throw error;
  }
}

export async function applyPurchaseFeeWithReferralFromOrder(params: {
  buyerId: string;
  amount: number;
  referralId?: string;
  purchaseFeeBPS: number;
  referrerFeeBPS: number;
}) {
  const { buyerId, amount, referralId, purchaseFeeBPS, referrerFeeBPS } =
    params;
  try {
    if (!purchaseFeeBPS || purchaseFeeBPS <= 0) {
      return { totalFee: 0, referralFee: 0, marketplaceFee: 0 };
    }

    const totalFeeAmount = calculateFeeAmount(amount, purchaseFeeBPS);
    if (totalFeeAmount <= 0) {
      return { totalFee: 0, referralFee: 0, marketplaceFee: 0 };
    }

    // Validate buyer has sufficient funds for total fee
    await validateSufficientFunds({
      userId: buyerId,
      amount: totalFeeAmount,
      operation: "purchase fee with referral from order",
    });

    // Deduct total fee from buyer
    await updateUserBalance({
      userId: buyerId,
      sumChange: -totalFeeAmount,
      lockedChange: 0,
    });

    let referralFeeAmount = 0;
    let marketplaceFeeAmount = totalFeeAmount;

    // If there's a referral ID, check for custom or default referrer fee
    if (referralId && referrerFeeBPS > 0) {
      const referrerQuery = await DBUserCollection.where("id", "==", referralId)
        .limit(1)
        .get();

      if (!referrerQuery.empty) {
        const referrerDoc = referrerQuery.docs[0];
        const referrerId = referrerDoc.id;
        const referrerData = referrerDoc.data();

        // Check for custom referral fee first, then fall back to order fee
        let referrerFeeRate = 0;
        if (
          referrerData.referral_fee !== undefined &&
          referrerData.referral_fee > 0
        ) {
          // Use custom referral fee
          referrerFeeRate = referrerData.referral_fee;
          FeeServiceLogger.logCustomReferralFee({
            referrerFeeRate,
            referrerId,
          });
        } else {
          // Use referral fee from order
          referrerFeeRate = referrerFeeBPS;
          FeeServiceLogger.logOrderReferralFee({
            referrerFeeRate,
            referrerId,
          });
        }

        if (referrerFeeRate > 0) {
          referralFeeAmount = calculateFeeAmount(amount, referrerFeeRate);
          marketplaceFeeAmount = safeSubtract(
            totalFeeAmount,
            referralFeeAmount
          );

          // Add referral fee to referrer's balance
          await addFundsWithHistory({
            userId: referrerId,
            amount: referralFeeAmount,
            txType: TxType.REFERRAL_FEE,
            descriptionIntlKey:
              TRANSACTION_DESCRIPTION_INTL_KEYS.REFERRAL_FEE_FROM_PURCHASE,
            descriptionIntlParams: {
              amount: referralFeeAmount.toString(),
            },
          });

          FeeServiceLogger.logReferralFeeApplied({
            feeAmount: referralFeeAmount,
            feeType: "purchase_referral",
            referrerFeeRate,
            referrerId,
            referralId,
          });
        }
      } else {
        FeeServiceLogger.logReferrerNotFound({ referralId });
      }
    }

    // Apply remaining fee to marketplace revenue
    if (marketplaceFeeAmount > 0) {
      await applyFeeToMarketplaceRevenue({
        feeAmount: marketplaceFeeAmount,
        feeType: "purchase",
      });
    }

    FeeServiceLogger.logTotalFeeApplied({
      feeAmount: totalFeeAmount,
      feeType: "purchase_total",
      referralFee: referralFeeAmount,
      marketplaceFee: marketplaceFeeAmount,
    });

    return {
      totalFee: totalFeeAmount,
      referralFee: referralFeeAmount,
      marketplaceFee: marketplaceFeeAmount,
    };
  } catch (error) {
    FeeServiceLogger.logFeeServiceError({
      error,
      operation: LogOperations.PURCHASE_FEE_WITH_REFERRAL,
    });
    throw error;
  }
}

export async function applyWithdrawFee(userId: string, withdrawAmount: number) {
  try {
    const config = await getAppConfig();
    if (!config?.withdrawal_fee) {
      return 0;
    }

    // Withdraw fee is now a static TON value, not BPS
    const feeAmount = config.withdrawal_fee;
    if (feeAmount <= 0 || feeAmount >= withdrawAmount) {
      return 0;
    }

    await applyFeeToMarketplaceRevenue({
      feeAmount,
      feeType: "withdrawal",
    });

    FeeServiceLogger.logFeeApplied({
      feeAmount,
      feeType: "withdrawal",
      userId,
    });

    return feeAmount;
  } catch (error) {
    FeeServiceLogger.logFeeServiceError({
      error,
      operation: LogOperations.WITHDRAWAL_FEE,
      userId,
      amount: withdrawAmount,
    });
    throw error;
  }
}
