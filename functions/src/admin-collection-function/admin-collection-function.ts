import { HttpsError, onCall } from "firebase-functions/v2/https";
import { commonFunctionsConfig, LogOperations } from "../constants";
import {
  validateAdminUser,
  recalculateOrderDeadlines,
  clearOrderDeadlines,
} from "./admin-collection-function.service";
import { AdminCollectionLogger } from "./admin-collection-function.logger";
import { throwAdminCollectionInternalError } from "./admin-collection-function.error-handler";
import { requireAuth } from "src/services/common.validator";

export const recalculateDeadlines = onCall<{
  collectionId: string;
}>(commonFunctionsConfig, async (request) => {
  requireAuth(request);

  const { collectionId } = request.data;
  const userId = request.auth.uid;

  try {
    await validateAdminUser(userId);
    const updatedCount = await recalculateOrderDeadlines(collectionId);

    AdminCollectionLogger.logAdminCollectionOperation({
      operation: LogOperations.RECALCULATE_DEADLINES,
      collectionId,
      updatedCount,
      userId,
    });

    return {
      success: true,
      message: `Successfully recalculated deadlines for ${updatedCount} orders`,
      updatedCount,
    };
  } catch (error) {
    AdminCollectionLogger.logAdminCollectionError({
      error,
      operation: LogOperations.RECALCULATE_DEADLINES,
      collectionId,
      userId,
    });

    if (error instanceof HttpsError) {
      throw error;
    }
    throwAdminCollectionInternalError((error as any).message);
  }
});

export const clearDeadlines = onCall<{
  collectionId: string;
}>(commonFunctionsConfig, async (request) => {
  requireAuth(request);

  const { collectionId } = request.data;
  const userId = request.auth.uid;

  try {
    await validateAdminUser(userId);
    const updatedCount = await clearOrderDeadlines(collectionId);

    AdminCollectionLogger.logAdminCollectionOperation({
      operation: LogOperations.CLEAR_DEADLINES,
      collectionId,
      updatedCount,
      userId,
    });

    return {
      success: true,
      message: `Successfully cleared deadlines for ${updatedCount} orders`,
      updatedCount,
    };
  } catch (error) {
    AdminCollectionLogger.logAdminCollectionError({
      error,
      operation: LogOperations.CLEAR_DEADLINES,
      collectionId,
      userId,
    });

    if (error instanceof HttpsError) {
      throw error;
    }
    throwAdminCollectionInternalError((error as any).message);
  }
});
