import { Context } from "telegraf";
import { PREM_RELAYER_USERNAME } from "../../app.constants";
import { updateUserSession } from "../../services/session-service/session-service";
import { T } from "../../i18n";
import { botMessages } from "../../intl/messages";
import { createMarketplaceInlineKeyboard } from "../../utils/keyboards";
import { WithdrawGiftCallbackLogger } from "./withdraw-gift-callback.logger";
import { handleGiftWithdrawal } from "../../flows/gift-withdrawal-flow";
import { loadEnvironment } from "../../config/env-loader";

export const handleWithdrawGiftCallback = async (ctx: Context) => {
  try {
    // Load environment to check NODE_ENV
    loadEnvironment();
    const isProduction = process.env.NODE_ENV === "production";

    // @ts-expect-error note
    const callbackData = ctx.callbackQuery?.data;
    const tgId = ctx.from?.id?.toString();

    if (!callbackData || !tgId || !ctx.chat?.id) {
      await ctx.answerCbQuery("Invalid request");
      return;
    }

    // Extract gift ID from callback data (format: withdraw_gift_{giftId})
    const giftId = callbackData.replace("withdraw_gift_", "");

    if (!giftId) {
      await ctx.answerCbQuery("Invalid gift ID");
      return;
    }

    WithdrawGiftCallbackLogger.logWithdrawGiftCallbackStarted({
      tgId,
      giftId,
      chatId: ctx.chat.id,
    });

    // Save the selected gift ID to the user's bot session
    await updateUserSession(tgId, {
      withdrawal_gift_id: giftId,
    });

    WithdrawGiftCallbackLogger.logGiftIdSavedToSession({
      tgId,
      giftId,
    });

    if (isProduction) {
      // PRODUCTION: Show instructions to go to @premrelayer
      const instructionMessage = T(
        ctx,
        botMessages.giftWithdrawalInstructions.id,
        {
          relayerUsername: PREM_RELAYER_USERNAME,
        }
      );

      await ctx.editMessageText(
        instructionMessage,
        createMarketplaceInlineKeyboard(ctx)
      );

      await ctx.answerCbQuery(T(ctx, botMessages.giftSelectedForWithdrawal.id));
    } else {
      // DEVELOPMENT: Trigger handleGiftWithdrawal directly
      const withdrawalContext = {
        ctx,
        chat_id: ctx.chat.id,
        userId: tgId,
        withdrawalGiftId: giftId,
      };

      const success = await handleGiftWithdrawal(withdrawalContext);

      if (success) {
        // Show simulation success message
        await ctx.editMessageText(
          T(ctx, botMessages.giftWithdrawalSimulationSuccess.id),
          createMarketplaceInlineKeyboard(ctx)
        );
        await ctx.answerCbQuery(
          T(ctx, botMessages.giftWithdrawalSimulatedSuccessfully.id)
        );
      } else {
        // Show simulation failure message
        await ctx.editMessageText(
          T(ctx, botMessages.giftWithdrawalSimulationFailed.id),
          createMarketplaceInlineKeyboard(ctx)
        );
        await ctx.answerCbQuery(
          T(ctx, botMessages.giftWithdrawalSimulationFailedCallback.id)
        );
      }
    }

    WithdrawGiftCallbackLogger.logWithdrawGiftCallbackCompleted({
      tgId,
      giftId,
    });
  } catch (error) {
    WithdrawGiftCallbackLogger.logWithdrawGiftCallbackError({
      error,
      tgId: ctx.from?.id?.toString() ?? "unknown",
      chatId: ctx.chat?.id ?? 0,
    });

    await ctx.answerCbQuery(T(ctx, botMessages.botGenericError.id));
  }
};
