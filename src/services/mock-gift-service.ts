import { OrderGift } from "../marketplace-shared";

const MODEL_NAMES = [
  "Andromeda",
  "<PERSON>",
  "Bitter Lemon",
  "Blender",
  "Blood Ember",
  "Blood Sucker",
  "Blue Goo",
  "Blueberry Pie",
  "Bubblegum",
  "Cappuccino",
  "Caramel",
  "Celestia",
  "Cherry Berry",
  "Cherry Cola",
  "Cherry Milk",
  "Choco Mint",
  "Choco Pop",
];

const PATTERN_NAMES = [
  "Acorn",
  "Alert Serpent",
  "All-Seeing Eye",
  "Anchor",
  "Ancient Urn",
  "Anubis",
  "Apple",
  "Arcane Mirror",
  "Aztec Falcon",
  "Aztec Totem",
  "Baby Bottle",
  "Ball",
];

const getRandomElement = <T>(array: T[]): T => {
  // @ts-expect-error note
  return array[Math.floor(Math.random() * array.length)];
};

const getRandomNumber = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

const getRandomRarity = (): number => {
  return getRandomNumber(0, 1000);
};

export const generateMockGift = (): OrderGift => {
  return {
    base_name: "Lol Pop",
    owned_gift_id: getRandomNumber(1, 200).toString(),
    backdrop: {
      name: getRandomElement(PATTERN_NAMES),
      colors: {
        center_color: 8309634,
        edge_color: 4562522,
        symbol_color: 158498,
        text_color: 12451788,
      },
      rarity_per_mille: getRandomRarity(),
    },
    model: {
      name: getRandomElement(MODEL_NAMES),
      rarity_per_mille: getRandomRarity(),
    },
    symbol: {
      name: getRandomElement(PATTERN_NAMES),
      rarity_per_mille: getRandomRarity(),
    },
  };
};
