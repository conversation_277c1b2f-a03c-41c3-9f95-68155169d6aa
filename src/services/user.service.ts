import { DBUserCollection } from "../firebase/firebase-admin";
import { UserEntity } from "../marketplace-shared";

export interface UserData {
  tg_id: string;
  userLanguage?: string;
}

export async function getUserById(userId: string): Promise<UserEntity | null> {
  try {
    const userDoc = await DBUserCollection.doc(userId).get();

    if (!userDoc.exists) {
      return null;
    }

    return { id: userDoc.id, ...userDoc.data() } as UserEntity;
  } catch (error) {
    throw new Error(`Failed to get user by ID: ${error}`);
  }
}

export async function findUserIdByTgId(tgId: string) {
  try {
    const usersQuery = await DBUserCollection.where("tg_id", "==", tgId)
      .limit(1)
      .get();

    if (usersQuery.empty) {
      return {
        success: false,
        message: "User not found with the provided Telegram ID.",
      };
    }

    const userDoc = usersQuery.docs[0];
    if (!userDoc) {
      return {
        success: false,
        message: "User document not found.",
      };
    }

    return {
      success: true,
      userId: userDoc.id,
    };
  } catch (error) {
    throw new Error(`Failed to find user by Telegram ID: ${error}`);
  }
}

export async function resolveUserId(params: {
  userId?: string;
  tgId?: string;
}): Promise<{
  success: boolean;
  userId?: string;
  message?: string;
}> {
  const { userId, tgId } = params;

  if (userId) {
    return {
      success: true,
      userId,
    };
  }

  if (tgId) {
    return await findUserIdByTgId(tgId);
  }

  return {
    success: false,
    message: "Either userId or tgId is required.",
  };
}
