import * as admin from "firebase-admin";
import { BotSessionEntity } from "../marketplace-shared";
import { SupportedLocale } from "../i18n";
import { log } from "../utils/logger";
import { DBBotSessionsCollection } from "../firebase/firebase-admin";

const CACHE_CLEANUP_INTERVAL = 60 * 60 * 1000; // 1 hour in milliseconds

// In-memory cache for language preferences
const languageCache = new Map<string, string>();

// Initialize cache cleanup interval
setInterval(() => {
  const cacheSize = languageCache.size;
  languageCache.clear();
  log.info("Language preference cache cleared", {
    operation: "cache_cleanup",
    previousCacheSize: cacheSize,
  });
}, CACHE_CLEANUP_INTERVAL);

export class LanguagePreferenceService {
  static async setUserLanguage(
    tgId: string,
    language: SupportedLocale
  ): Promise<void> {
    try {
      const sessionDoc = DBBotSessionsCollection.doc(tgId);

      // If language is English (default), remove the field to optimize storage
      if (language === SupportedLocale.EN) {
        await sessionDoc.update({
          language_preference: admin.firestore.FieldValue.delete(),
          updatedAt: new Date(),
        });
        // Remove from cache as well
        languageCache.delete(tgId);
      } else {
        // Store non-English language preferences
        await sessionDoc.set(
          {
            id: tgId,
            language_preference: language,
            updatedAt: new Date(),
          },
          { merge: true }
        );
        // Update cache
        languageCache.set(tgId, language);
      }

      log.info("User language preference updated", {
        operation: "set_user_language",
        tgId,
        language,
        cached: language !== SupportedLocale.EN,
      });
    } catch (error) {
      log.error("Failed to set user language preference", error, {
        operation: "set_user_language",
        tgId,
        language,
      });
      throw error;
    }
  }

  static async getUserLanguage(tgId: string): Promise<SupportedLocale> {
    try {
      // First check cache
      const cachedLanguage = languageCache.get(tgId);
      if (cachedLanguage) {
        log.info("Language preference retrieved from cache", {
          operation: "get_user_language",
          tgId,
          language: cachedLanguage,
          source: "cache",
        });
        return cachedLanguage as SupportedLocale;
      }

      const sessionDoc = await DBBotSessionsCollection.doc(tgId).get();

      if (sessionDoc.exists) {
        const sessionData = sessionDoc.data() as BotSessionEntity;
        const language = sessionData.language_preference;

        if (language && language !== SupportedLocale.EN) {
          // Add to cache for future requests
          languageCache.set(tgId, language);
          log.info("Language preference retrieved from Firebase and cached", {
            operation: "get_user_language",
            tgId,
            language,
            source: "firebase",
          });
          return language as SupportedLocale;
        }
      }

      // Default to English if no preference found
      log.info("No language preference found, defaulting to English", {
        operation: "get_user_language",
        tgId,
        language: SupportedLocale.EN,
        source: "default",
      });
      return SupportedLocale.EN;
    } catch (error) {
      log.error("Failed to get user language preference", error, {
        operation: "get_user_language",
        tgId,
      });
      // Return default language on error
      return SupportedLocale.EN;
    }
  }

  /**
   * Check if user has an explicit language preference stored
   * Returns true only if user has a non-English preference stored
   */
  static async hasExplicitLanguagePreference(tgId: string): Promise<boolean> {
    try {
      // Check cache first
      const cachedLanguage = languageCache.get(tgId);
      if (cachedLanguage) {
        return true; // If it's in cache, it's a non-English preference
      }

      // Check Firebase

      const sessionDoc = await DBBotSessionsCollection.doc(tgId).get();

      if (sessionDoc.exists) {
        const sessionData = sessionDoc.data() as BotSessionEntity;
        const hasExplicitPreference = !!(
          sessionData.language_preference &&
          sessionData.language_preference !== SupportedLocale.EN
        );

        log.info("Explicit language preference check completed", {
          operation: "has_explicit_language_preference",
          tgId,
          hasExplicitPreference,
        });

        return hasExplicitPreference;
      }

      return false;
    } catch (error) {
      log.error("Failed to check explicit language preference", error, {
        operation: "has_explicit_language_preference",
        tgId,
      });
      return false;
    }
  }
}
