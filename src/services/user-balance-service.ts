import { AppDate, TxType, UserEntity } from "../marketplace-shared";
import { log } from "../utils/logger";
import { db, DBUserCollection } from "../firebase/firebase-admin";

export interface UserBalance {
  sum: number;
  locked: number;
}

export async function getUserBalance(userId: string): Promise<UserBalance> {
  try {
    const userDoc = await DBUserCollection.doc(userId).get();

    if (!userDoc.exists) {
      throw new Error(`User ${userId} not found`);
    }

    const userData = userDoc.data() as UserEntity;
    return userData.balance || { sum: 0, locked: 0 };
  } catch (error) {
    log.error("Failed to get user balance", error, {
      operation: "get_user_balance",
      userId,
    });
    throw error;
  }
}

export async function updateUserBalance(params: {
  userId: string;
  sumChange: number;
  lockedChange: number;
}): Promise<UserBalance> {
  const { userId, sumChange, lockedChange } = params;

  try {
    const userRef = DBUserCollection.doc(userId);

    const result = await db.runTransaction(async (transaction) => {
      const userDoc = await transaction.get(userRef);

      if (!userDoc.exists) {
        throw new Error(`User ${userId} not found`);
      }

      const userData = userDoc.data() as UserEntity;
      const currentBalance = userData.balance || { sum: 0, locked: 0 };

      const newBalance = {
        sum: Math.max(0, currentBalance.sum + sumChange),
        locked: Math.max(0, currentBalance.locked + lockedChange),
      };

      transaction.update(userRef, {
        balance: newBalance,
        updatedAt: new Date() as AppDate,
      });

      return newBalance;
    });

    log.info("User balance updated successfully", {
      operation: "update_user_balance",
      userId,
      sumChange,
      lockedChange,
      newBalance: result,
    });

    return result;
  } catch (error) {
    log.error("Failed to update user balance", error, {
      operation: "update_user_balance",
      userId,
      sumChange,
      lockedChange,
    });
    throw error;
  }
}

export async function spendFundsWithHistory(params: {
  userId: string;
  amount: number;
  txType: TxType;
  orderId?: string;
  description?: string;
  descriptionIntlKey?: string;
  descriptionIntlParams?: Record<string, string | number>;
}): Promise<UserBalance> {
  const {
    userId,
    amount,
    txType,
    orderId,
    description,
    descriptionIntlKey,
    descriptionIntlParams,
  } = params;

  if (amount <= 0) {
    throw new Error("Amount must be positive");
  }

  try {
    const currentBalance = await getUserBalance(userId);
    const availableBalance = currentBalance.sum - currentBalance.locked;

    if (availableBalance < amount) {
      throw new Error(
        `Insufficient available balance to spend ${amount} TON. Available: ${availableBalance} TON`
      );
    }

    const newBalance = await updateUserBalance({
      userId,
      sumChange: -amount,
      lockedChange: 0,
    });

    // Create transaction record
    await createTransactionRecord({
      userId,
      txType,
      amount: -amount, // Negative for spending
      ...(orderId && { orderId }),
      ...(description && { description }),
      ...(descriptionIntlKey && { descriptionIntlKey }),
      ...(descriptionIntlParams && { descriptionIntlParams }),
    });

    log.info("Funds spent with history recorded", {
      operation: "spend_funds_with_history",
      userId,
      amount,
      txType,
      orderId,
    });

    return newBalance;
  } catch (error) {
    log.error("Failed to spend funds with history", error, {
      operation: "spend_funds_with_history",
      userId,
      amount,
      txType,
    });
    throw error;
  }
}

async function createTransactionRecord(params: {
  userId: string;
  txType: TxType;
  amount: number;
  orderId?: string;
  description?: string;
  descriptionIntlKey?: string;
  descriptionIntlParams?: Record<string, string | number>;
}): Promise<void> {
  const {
    userId,
    txType,
    amount,
    orderId,
    description,
    descriptionIntlKey,
    descriptionIntlParams,
  } = params;

  try {
    const transactionRef = DBUserCollection.doc(userId)
      .collection("transactions")
      .doc();

    const transactionData = {
      id: transactionRef.id,
      tx_type: txType,
      amount,
      order_id: orderId || null,
      description: description || null,
      description_intl_key: descriptionIntlKey || null,
      description_intl_params: descriptionIntlParams || null,
      createdAt: new Date() as AppDate,
    };

    await transactionRef.set(transactionData);

    log.info("Transaction record created", {
      operation: "create_transaction_record",
      userId,
      transactionId: transactionRef.id,
      txType,
      amount,
    });
  } catch (error) {
    log.error("Failed to create transaction record", error, {
      operation: "create_transaction_record",
      userId,
      txType,
      amount,
    });
    throw error;
  }
}
