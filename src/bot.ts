import { Telegraf } from "telegraf";
import { loadEnvironment } from "./config/env-loader";
import {
  handleContactSupportButton,
  handleDepositGiftButton,
  handleGetMyGiftsButton,
} from "./handlers/buttons";
import {
  handleBackToMenuCallback,
  handleContactSupportCallback,
  handleOpenMarketplaceCallback,
  handleOrderHelpCallback,
  handleWithdrawGiftCallback,
} from "./handlers/callbacks/index";
import {
  handleHealthCommand,
  handleHelpCommand,
  handleStartCommand,
} from "./handlers/commands";
import { T } from "./i18n";
import { botMessages } from "./intl/messages";
import { businessConnectionMiddleware } from "./middleware/business-connection";
import {
  handleLanguageSwitch,
  languageDetectionMiddleware,
} from "./middleware/language-detection";
import { ButtonTextPatterns } from "./utils/button-text-matcher";
import { BotCommands, CallbackActions } from "./app.constants";

loadEnvironment();

const BOT_TOKEN = process.env.BOT_TOKEN;

if (!BOT_TOKEN) {
  throw new Error("BOT_TOKEN is required in environment variables list.");
}

const bot = new Telegraf(BOT_TOKEN);

bot.use(businessConnectionMiddleware);
bot.use(languageDetectionMiddleware);

bot.start(handleStartCommand);
bot.help(handleHelpCommand);
bot.command(BotCommands.HEALTH, handleHealthCommand);

bot.hears(ButtonTextPatterns.MY_GIFTS, handleGetMyGiftsButton);
bot.hears(ButtonTextPatterns.DEPOSIT_GIFT, handleDepositGiftButton);
bot.hears(ButtonTextPatterns.CONTACT_SUPPORT, handleContactSupportButton);

bot.action(CallbackActions.ORDER_HELP, handleOrderHelpCallback);
bot.action(CallbackActions.CONTACT_SUPPORT, handleContactSupportCallback);
bot.action(CallbackActions.OPEN_MARKETPLACE, handleOpenMarketplaceCallback);

bot.action(CallbackActions.BACK_TO_MENU, handleBackToMenuCallback);

// Gift withdrawal callbacks
bot.action(/^withdraw_gift_/, handleWithdrawGiftCallback);

// Language switch callbacks
bot.action(/^lang_/, handleLanguageSwitch);

bot.catch((err, ctx) => {
  console.log("Error in bot", err);
  ctx?.reply?.(T(ctx, botMessages.botGenericError.id));
});

export default bot;
