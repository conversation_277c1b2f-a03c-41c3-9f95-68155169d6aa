import { ButtonMessageIds } from "../app.constants";
import { T, TNoContext, SupportedLocale } from "../i18n";

export function getAllButtonTexts(messageId: string): string[] {
  const texts: string[] = [];

  // Get English text (default)
  texts.push(TNoContext(messageId));

  // Get Ukrainian text
  const mockUkContext = { userLanguage: SupportedLocale.UA };
  texts.push(T(mockUkContext, messageId));

  // Get Russian text
  const mockRuContext = { userLanguage: SupportedLocale.RU };
  texts.push(T(mockRuContext, messageId));

  // Remove duplicates and return
  return [...new Set(texts)];
}

export function createButtonTextPattern(messageId: string): RegExp {
  const texts = getAllButtonTexts(messageId);
  // Escape special regex characters and create pattern
  const escapedTexts = texts.map((text) =>
    text.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")
  );
  return new RegExp(`^(${escapedTexts.join("|")})$`);
}

// Dynamic button text patterns that update with language changes
export function getButtonTextPattern(messageId: string): RegExp {
  return createButtonTextPattern(messageId);
}

// Static patterns for backward compatibility - these include all language variants
export const ButtonTextPatterns = {
  MY_GIFTS: createButtonTextPattern(ButtonMessageIds.MY_GIFTS),
  DEPOSIT_GIFT: createButtonTextPattern(ButtonMessageIds.DEPOSIT_GIFT),
  CONTACT_SUPPORT: createButtonTextPattern(ButtonMessageIds.CONTACT_SUPPORT),
} as const;
