import winston from "winston";
import { LoggingWinston } from "@google-cloud/logging-winston";
import { FIREBASE_PROJECT_ID, LogOperations } from "../app.constants";

function createLoggingWinston(): LoggingWinston | null {
  const projectId = FIREBASE_PROJECT_ID;
  const keyFilename = process.env.GOOGLE_APPLICATION_CREDENTIALS;

  // Only create Google Cloud logging if we have proper credentials
  if (!projectId) {
    console.warn(
      "⚠️ FIREBASE_PROJECT_ID not found, skipping Google Cloud logging"
    );
    return null;
  }

  const config: any = {
    logName: "marketplace-bot",
    projectId: projectId,
  };

  if (keyFilename) {
    config.keyFilename = keyFilename;
  }

  try {
    return new LoggingWinston(config);
  } catch (error) {
    console.warn(
      "⚠️ Failed to initialize Google Cloud logging, falling back to console only:",
      error
    );
    return null;
  }
}

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL ?? "info",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: {
    service: "marketplace-bot",
    environment: process.env.NODE_ENV ?? "development",
  },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
    }),
  ],
});

// Only add Google Cloud logging if credentials are available and it's enabled
const shouldEnableCloudLogging =
  process.env.NODE_ENV === "production" ||
  process.env.ENABLE_CLOUD_LOGGING === "true";

if (shouldEnableCloudLogging) {
  const loggingWinston = createLoggingWinston();
  if (loggingWinston) {
    logger.add(loggingWinston);
    console.log("✅ Google Cloud logging enabled");
  } else {
    console.log(
      "ℹ️ Google Cloud logging disabled - using console logging only"
    );
  }
} else {
  console.log("ℹ️ Google Cloud logging disabled by configuration");
}

interface LogContext {
  userId?: string | number;
  chatId?: string | number | undefined;
  messageId?: string;
  operation?: string;
  [key: string]: any;
}

class Logger {
  private readonly winston: winston.Logger;

  constructor(winstonLogger: winston.Logger) {
    this.winston = winstonLogger;
  }

  info(message: string, context?: LogContext) {
    this.winston.info(message, context);
  }

  error(message: string, error?: Error | any, context?: LogContext) {
    const logData = {
      ...context,
      error:
        error instanceof Error
          ? {
              message: error.message,
              stack: error.stack,
              name: error.name,
            }
          : error,
    };
    this.winston.error(message, logData);
  }

  warn(message: string, context?: LogContext) {
    this.winston.warn(message, context);
  }

  debug(message: string, context?: LogContext) {
    this.winston.debug(message, context);
  }

  botLog(
    message: string,
    botData: {
      userId?: string;
      chatId?: string;
      messageId?: string;
      operation?: string;
      [key: string]: any;
    }
  ) {
    this.info(message, {
      operation: LogOperations.BOT_OPERATION,
      ...botData,
    });
  }

  webhookLog(
    message: string,
    webhookData: {
      method?: string;
      url?: string;
      status?: string;
      [key: string]: any;
    }
  ) {
    this.info(message, {
      operation: LogOperations.WEBHOOK_OPERATION,
      ...webhookData,
    });
  }

  healthLog(
    message: string,
    healthData: {
      status?: string;
      timestamp?: string;
      [key: string]: any;
    }
  ) {
    this.info(message, {
      operation: LogOperations.HEALTH_CHECK,
      ...healthData,
    });
  }
}

export const log = new Logger(logger);

export type { LogContext };
